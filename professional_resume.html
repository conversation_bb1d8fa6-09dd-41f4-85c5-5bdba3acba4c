<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Resume</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
        }

        .container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header .title {
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .main-content {
            padding: 30px;
        }

        .section {
            margin-bottom: 30px;
        }

        .section h2 {
            color: #667eea;
            font-size: 1.4em;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 2px solid #667eea;
        }

        .experience-item, .education-item, .project-item {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .item-title {
            font-weight: bold;
            color: #333;
        }

        .item-company {
            color: #667eea;
            font-weight: 500;
        }

        .item-date {
            color: #666;
            font-size: 0.9em;
            white-space: nowrap;
        }

        .item-description {
            margin-top: 10px;
        }

        .item-description ul {
            margin-left: 20px;
        }

        .item-description li {
            margin-bottom: 5px;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .skill-category h3 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .skill-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .skill-tag {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .summary {
            font-size: 1.1em;
            line-height: 1.8;
            color: #555;
            text-align: justify;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .contact-info {
                flex-direction: column;
                align-items: center;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .item-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .skills-grid {
                grid-template-columns: 1fr;
            }
        }

        @media print {
            body {
                background: white;
            }
            
            .container {
                box-shadow: none;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <h1>John Doe</h1>
            <div class="title">Senior Software Engineer</div>
            <div class="contact-info">
                <div class="contact-item">
                    <span>📧</span>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <span>📱</span>
                    <span>+1 (555) 123-4567</span>
                </div>
                <div class="contact-item">
                    <span>🌐</span>
                    <span>linkedin.com/in/johndoe</span>
                </div>
                <div class="contact-item">
                    <span>📍</span>
                    <span>San Francisco, CA</span>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Professional Summary -->
            <div class="section">
                <h2>Professional Summary</h2>
                <div class="summary">
                    Experienced Senior Software Engineer with 8+ years of expertise in full-stack development, 
                    cloud architecture, and team leadership. Proven track record of delivering scalable solutions 
                    and leading cross-functional teams to achieve business objectives. Passionate about emerging 
                    technologies and continuous learning.
                </div>
            </div>

            <!-- Experience Section -->
            <div class="section">
                <h2>Professional Experience</h2>
                
                <div class="experience-item">
                    <div class="item-header">
                        <div>
                            <div class="item-title">Senior Software Engineer</div>
                            <div class="item-company">TechCorp Inc.</div>
                        </div>
                        <div class="item-date">Jan 2021 - Present</div>
                    </div>
                    <div class="item-description">
                        <ul>
                            <li>Led development of microservices architecture serving 10M+ users daily</li>
                            <li>Implemented CI/CD pipelines reducing deployment time by 60%</li>
                            <li>Mentored 5 junior developers and conducted technical interviews</li>
                            <li>Optimized database queries resulting in 40% performance improvement</li>
                        </ul>
                    </div>
                </div>

                <div class="experience-item">
                    <div class="item-header">
                        <div>
                            <div class="item-title">Software Engineer</div>
                            <div class="item-company">StartupXYZ</div>
                        </div>
                        <div class="item-date">Mar 2019 - Dec 2020</div>
                    </div>
                    <div class="item-description">
                        <ul>
                            <li>Developed RESTful APIs using Node.js and Express.js</li>
                            <li>Built responsive web applications with React and Redux</li>
                            <li>Collaborated with product team to define technical requirements</li>
                            <li>Implemented automated testing increasing code coverage to 85%</li>
                        </ul>
                    </div>
                </div>

                <div class="experience-item">
                    <div class="item-header">
                        <div>
                            <div class="item-title">Junior Software Developer</div>
                            <div class="item-company">WebSolutions Ltd.</div>
                        </div>
                        <div class="item-date">Jun 2017 - Feb 2019</div>
                    </div>
                    <div class="item-description">
                        <ul>
                            <li>Developed and maintained web applications using PHP and MySQL</li>
                            <li>Created responsive UI components with HTML5, CSS3, and JavaScript</li>
                            <li>Participated in agile development processes and daily standups</li>
                            <li>Fixed bugs and implemented feature requests from clients</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Latest Projects Section -->
            <div class="section">
                <h2>Latest 5 Projects</h2>
                
                <div class="project-item">
                    <div class="item-header">
                        <div>
                            <div class="item-title">E-Commerce Platform Redesign</div>
                            <div class="item-company">React, Node.js, MongoDB, AWS</div>
                        </div>
                        <div class="item-date">2024</div>
                    </div>
                    <div class="item-description">
                        Complete redesign of e-commerce platform handling 50K+ daily transactions. 
                        Implemented modern UI/UX, payment gateway integration, and real-time inventory management.
                    </div>
                </div>

                <div class="project-item">
                    <div class="item-header">
                        <div>
                            <div class="item-title">AI-Powered Analytics Dashboard</div>
                            <div class="item-company">Python, TensorFlow, React, PostgreSQL</div>
                        </div>
                        <div class="item-date">2023</div>
                    </div>
                    <div class="item-description">
                        Built machine learning dashboard for business intelligence with predictive analytics, 
                        data visualization, and automated reporting features.
                    </div>
                </div>

                <div class="project-item">
                    <div class="item-header">
                        <div>
                            <div class="item-title">Mobile Banking Application</div>
                            <div class="item-company">React Native, Node.js, Redis, Docker</div>
                        </div>
                        <div class="item-date">2023</div>
                    </div>
                    <div class="item-description">
                        Cross-platform mobile app for banking services with biometric authentication, 
                        real-time notifications, and secure transaction processing.
                    </div>
                </div>

                <div class="project-item">
                    <div class="item-header">
                        <div>
                            <div class="item-title">Cloud Migration Project</div>
                            <div class="item-company">AWS, Kubernetes, Terraform, Jenkins</div>
                        </div>
                        <div class="item-date">2022</div>
                    </div>
                    <div class="item-description">
                        Led migration of legacy systems to AWS cloud infrastructure, implementing 
                        containerization and infrastructure as code practices.
                    </div>
                </div>

                <div class="project-item">
                    <div class="item-header">
                        <div>
                            <div class="item-title">Real-time Chat Application</div>
                            <div class="item-company">Socket.io, Vue.js, Express.js, MongoDB</div>
                        </div>
                        <div class="item-date">2022</div>
                    </div>
                    <div class="item-description">
                        Developed real-time messaging platform with file sharing, group chats, 
                        and end-to-end encryption capabilities.
                    </div>
                </div>
            </div>

            <!-- Skills Section -->
            <div class="section">
                <h2>Technical Skills</h2>
                <div class="skills-grid">
                    <div class="skill-category">
                        <h3>Programming Languages</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">JavaScript</span>
                            <span class="skill-tag">Python</span>
                            <span class="skill-tag">Java</span>
                            <span class="skill-tag">TypeScript</span>
                            <span class="skill-tag">PHP</span>
                        </div>
                    </div>

                    <div class="skill-category">
                        <h3>Frontend Technologies</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">React</span>
                            <span class="skill-tag">Vue.js</span>
                            <span class="skill-tag">Angular</span>
                            <span class="skill-tag">HTML5/CSS3</span>
                            <span class="skill-tag">Sass</span>
                        </div>
                    </div>

                    <div class="skill-category">
                        <h3>Backend Technologies</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">Node.js</span>
                            <span class="skill-tag">Express.js</span>
                            <span class="skill-tag">Django</span>
                            <span class="skill-tag">Spring Boot</span>
                            <span class="skill-tag">Laravel</span>
                        </div>
                    </div>

                    <div class="skill-category">
                        <h3>Databases</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">MongoDB</span>
                            <span class="skill-tag">PostgreSQL</span>
                            <span class="skill-tag">MySQL</span>
                            <span class="skill-tag">Redis</span>
                            <span class="skill-tag">DynamoDB</span>
                        </div>
                    </div>

                    <div class="skill-category">
                        <h3>Cloud & DevOps</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">AWS</span>
                            <span class="skill-tag">Docker</span>
                            <span class="skill-tag">Kubernetes</span>
                            <span class="skill-tag">Jenkins</span>
                            <span class="skill-tag">Terraform</span>
                        </div>
                    </div>

                    <div class="skill-category">
                        <h3>Tools & Others</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">Git</span>
                            <span class="skill-tag">Jira</span>
                            <span class="skill-tag">Agile</span>
                            <span class="skill-tag">REST APIs</span>
                            <span class="skill-tag">GraphQL</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Education Section -->
            <div class="section">
                <h2>Education</h2>

                <div class="education-item">
                    <div class="item-header">
                        <div>
                            <div class="item-title">Bachelor of Science in Computer Science</div>
                            <div class="item-company">University of Technology</div>
                        </div>
                        <div class="item-date">2013 - 2017</div>
                    </div>
                    <div class="item-description">
                        <ul>
                            <li>Graduated Magna Cum Laude with GPA: 3.8/4.0</li>
                            <li>Relevant Coursework: Data Structures, Algorithms, Database Systems, Software Engineering</li>
                            <li>Senior Project: Developed a web-based project management system</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Certifications Section -->
            <div class="section">
                <h2>Certifications</h2>

                <div class="education-item">
                    <div class="item-header">
                        <div>
                            <div class="item-title">AWS Certified Solutions Architect</div>
                            <div class="item-company">Amazon Web Services</div>
                        </div>
                        <div class="item-date">2023</div>
                    </div>
                </div>

                <div class="education-item">
                    <div class="item-header">
                        <div>
                            <div class="item-title">Certified Kubernetes Administrator (CKA)</div>
                            <div class="item-company">Cloud Native Computing Foundation</div>
                        </div>
                        <div class="item-date">2022</div>
                    </div>
                </div>

                <div class="education-item">
                    <div class="item-header">
                        <div>
                            <div class="item-title">Professional Scrum Master I (PSM I)</div>
                            <div class="item-company">Scrum.org</div>
                        </div>
                        <div class="item-date">2021</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
