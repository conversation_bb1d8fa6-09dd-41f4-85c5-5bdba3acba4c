# Professional Resume Template

This is a modern, responsive HTML/CSS resume template that you can easily customize with your own information.

## Features

- **Modern Design**: Clean, professional layout with gradient header
- **Responsive**: Works perfectly on desktop, tablet, and mobile devices
- **Print-Friendly**: Optimized for printing to PDF
- **Easy to Customize**: Simple HTML structure with clear sections
- **Professional Sections**: 
  - Header with contact information
  - Professional summary
  - Work experience
  - Latest 5 projects (as requested)
  - Technical skills organized by category
  - Education
  - Certifications

## How to Customize

### 1. Personal Information
Update the header section in `professional_resume.html`:
- Name
- Job title
- Email
- Phone number
- LinkedIn profile
- Location

### 2. Professional Summary
Edit the summary section to reflect your experience and career goals.

### 3. Work Experience
For each job, update:
- Job title
- Company name
- Employment dates
- Key achievements and responsibilities

### 4. Latest 5 Projects
This section showcases your most recent and relevant projects:
- Project name
- Technologies used
- Completion date
- Brief description of the project

### 5. Technical Skills
Update the skills section with your technologies:
- Programming languages
- Frontend technologies
- Backend technologies
- Databases
- Cloud & DevOps tools
- Other tools and methodologies

### 6. Education & Certifications
Add your educational background and professional certifications.

## Viewing the Resume

1. Open `professional_resume.html` in any web browser
2. The resume will display with professional formatting
3. Use your browser's print function to save as PDF

## Customization Tips

### Colors
The main color scheme uses a blue gradient (`#667eea` to `#764ba2`). To change colors:
- Update the `.header` background gradient
- Update the section headers color (`.section h2`)
- Update the skill tags background (`.skill-tag`)
- Update the left border color for items (`.experience-item`, `.education-item`, `.project-item`)

### Fonts
The template uses Arial font family. To change fonts:
- Update the `font-family` property in the `body` selector
- Consider using Google Fonts for more options

### Layout
The template is designed for an 800px max-width container. Adjust the `.container` max-width if needed.

## Converting to PDF

### Method 1: Browser Print
1. Open the HTML file in your browser
2. Press Ctrl+P (or Cmd+P on Mac)
3. Select "Save as PDF" as the destination
4. Adjust margins if needed
5. Save the PDF

### Method 2: Online Converters
Use online HTML to PDF converters for more control over the output.

## File Structure

```
Resume/
├── professional_resume.html    # Main resume file
├── README.md                  # This instruction file
└── himashu_CV.pdf            # Your existing PDF resume
```

## Browser Compatibility

This template works in all modern browsers:
- Chrome
- Firefox
- Safari
- Edge

## Mobile Responsiveness

The template includes responsive design that adapts to different screen sizes:
- Desktop: Full layout with side-by-side elements
- Tablet: Adjusted spacing and layout
- Mobile: Single column layout with stacked elements

## Tips for Professional Resume

1. **Keep it concise**: Aim for 1-2 pages maximum
2. **Use action verbs**: Start bullet points with strong action words
3. **Quantify achievements**: Include numbers and percentages where possible
4. **Tailor content**: Customize for each job application
5. **Proofread**: Check for spelling and grammar errors
6. **Update regularly**: Keep your skills and experience current

## Next Steps

1. Replace all placeholder content with your actual information
2. Customize the color scheme to match your personal brand
3. Add or remove sections as needed for your industry
4. Test the print/PDF output to ensure proper formatting
5. Keep the HTML file updated as your career progresses

## Support

If you need help customizing this template, consider:
- Learning basic HTML/CSS
- Using online HTML/CSS tutorials
- Consulting with a web developer for advanced customizations
